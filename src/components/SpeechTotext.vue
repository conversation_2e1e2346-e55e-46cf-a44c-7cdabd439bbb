<template>
  <!-- 主要内容区域 -->
  <div class="main-content">
    <div class="content-wrapper">
      <!-- 左侧音频转换区域 -->
      <div class="upload-card">
        <a-card class="upload-section-card">
          <!-- 1. 麦克风图标 -->
          <div class="microphone-section">
            <div class="microphone-icon">
              <img src="@/assets/asr_mic.svg" alt="麦克风图标" class="microphone-icon-img" />
            </div>
          </div>

          <!-- 2. 文字描述 -->
          <div class="description-section">
            <p class="description-text">点击/拖拽添加音视频，一键转文字生成纪要</p>
          </div>

          <!-- 3. 文件上传区域 -->
          <div class="upload-section">
            <a-upload-dragger
              :before-upload="handleBeforeUpload"
              :show-upload-list="true"
              :multiple="false"
              :max-count="1"
              :disabled="isUploading"
              accept=".mp3,.wav,.m4a,.aac,.flac"
              class="upload-dragger"
            >
              <div class="upload-content">
                <div class="upload-icon">
                  <svg viewBox="0 0 24 24" fill="currentColor" width="48" height="48">
                    <path
                      d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"
                    />
                  </svg>
                </div>
                <p class="upload-text">
                  {{ isUploading ? '正在上传中...' : '点击或拖拽文件到此区域上传' }}
                </p>
              </div>
            </a-upload-dragger>
          </div>

          <!-- 上传进度条 -->
          <div v-if="isUploading" class="upload-progress-section">
            <a-progress
              :percent="uploadState.progress.percentage"
              :status="isFailed ? 'exception' : 'active'"
              :show-info="true"
            />
            <p class="progress-text">
              {{ uploadState.progress.loaded }} / {{ uploadState.progress.total }} 字节
            </p>
          </div>

          <!-- 4. 开始转换按钮 -->
          <div class="action-section">
            <a-button
              type="primary"
              size="large"
              class="convert-btn-large"
              :disabled="!hasFile"
              @click="checkAuthAndExecute(startConversion)"
            >
              开始转换
            </a-button>
          </div>
        </a-card>
      </div>

      <!-- 右侧会议记录区域 -->
      <div class="transcription-card">
        <a-card class="transcription-section-card">
          <!-- 空状态 -->
          <div v-if="currentView === 'empty'" class="empty-state">
            <p class="empty-text">请选择音频并点击开始转换，一键转文字生成纪要。</p>
          </div>

          <!-- 列表状态 -->
          <div v-else-if="currentView === 'list'" class="list-state">
            <div class="list-header">
              <h3 class="list-title">近期语音转文字</h3>
              <div class="search-container">
                <a-input
                  v-model:value="searchKeyword"
                  placeholder="搜索历史记录"
                  class="search-input"
                >
                  <template #suffix>
                    <a-button
                      type="text"
                      size="large"
                      :loading="searchLoading"
                      class="search-btn"
                      @click="checkAuthAndExecute(() => search())"
                    >
                      查询
                    </a-button>
                  </template>
                </a-input>
              </div>
            </div>
            <div class="records-list">
              <el-scrollbar max-height="750px" @end-reached="loadMore">
                <div
                  v-for="record in transcriptionRecords"
                  :key="record.id"
                  class="record-item"
                  @click="selectRecord(record)"
                >
                  <span class="record-filename">{{ record.fileName }}</span>
                  <div class="record-status">
                    <a-spin v-if="record.status === 0" class="record-list-queue">
                      <span>排队中...</span>
                    </a-spin>
                    <a-spin v-else-if="record.status === 1" class="record-list-transcription">
                      <span>转写中...</span>
                    </a-spin>
                    <span v-else-if="record.status === -1" class="record-list-transcription-fail">
                      转写失败
                    </span>
                    <a-spin v-else-if="record.aiStatus === 1" class="record-list-transcription">
                      <span>总结中...</span>
                    </a-spin>
                    <span v-else-if="record.aiStatus === -1" class="record-list-transcription-fail">
                      总结失败
                    </span>
                    <span
                      v-else-if="record.status == 2 || record.aiStatus === 2"
                      class="record-list-transcription-success"
                    >
                      成功
                    </span>
                  </div>
                  <div class="record-actions">
                    <span class="record-time">{{ record.transcriptionTime }}</span>
                    <a-button
                      type="text"
                      danger
                      class="delete-btn"
                      @click.stop="deleteRecord(record.id)"
                    >
                      删除
                    </a-button>
                  </div>
                </div>
              </el-scrollbar>
            </div>
          </div>

          <!-- 详情状态 -->
          <div v-else-if="currentView === 'detail' && selectedRecord" class="detail-state">
            <div class="detail-header">
              <a-button type="text" class="back-btn" @click="backToList">← 返回</a-button>
            </div>
            <div class="detail-content">
              <!-- 没有AI总结时的单列布局 -->
              <div
                v-if="selectedRecord.aiStatus === 0 || selectedRecord.status === -1"
                class="single-column"
              >
                <h3 class="detail-title">{{ selectedRecord.fileName }}</h3>
                <div class="content-card">
                  <p class="transcription-content">{{ selectedRecord.content }}</p>
                </div>
                <div class="detail-actions">
                  <a-button
                    v-if="selectedRecord.aiStatus === 0 || selectedRecord.aiStatus === -1"
                    class="action-btn ai-summary-btn"
                    @click="checkAuthAndExecute(generateAISummary)"
                  >
                    AI纪要整理
                  </a-button>
                  <a-button class="action-btn save-as-btn" @click="checkAuthAndExecute(saveAs)">
                    另存为
                  </a-button>
                </div>
              </div>
              <!-- 有AI总结时的双列布局 -->
              <div v-else class="double-column-container">
                <div class="double-column">
                  <div class="left-column">
                    <h3 class="detail-title">{{ selectedRecord.fileName }}</h3>
                    <div class="content-card">
                      <p class="transcription-content">{{ selectedRecord.content }}</p>
                    </div>
                  </div>
                  <div class="right-column">
                    <h3 class="detail-title">AI整理纪要</h3>
                    <div class="summary-card">
                      <a-spin v-if="selectedRecord.aiStatus === 1"></a-spin>
                      <p class="summary-content">{{ selectedRecord.aiSummary }}</p>
                    </div>
                  </div>
                </div>
                <div class="detail-actions">
                  <a-button class="action-btn save-as-btn" @click="saveAs">另存为</a-button>
                </div>
              </div>
            </div>
          </div>
        </a-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import type { ScrollbarDirection } from 'element-plus'
import { useUpload } from '@/composables/useUpload'
import { useAuthStore } from '@/stores/auth'
import { TranscriptionService } from '@/services/transcriptionService'
import type { TranscriptionTaskData } from '@/types/api'

// 使用上传组合式函数
const {
  uploadState,
  currentFile,
  isUploading,
  isSuccess,
  isFailed,
  hasFile,
  selectFile,
  startUpload,
  resetUpload,
} = useUpload()

// 用户认证状态管理
const authStore = useAuthStore()

// 登录弹窗状态
const showLoginModal = ref(false)

// 转写记录接口定义
interface TranscriptionRecord {
  id: string
  fileName: string
  transcriptionTime: string
  status: number
  aiStatus: number
  content?: string
  aiSummary?: string
}

// 转写记录相关状态
const currentView = ref<'empty' | 'list' | 'detail'>('empty') // 当前视图状态
const selectedRecord = ref<TranscriptionRecord | null>(null) // 选中的转写记录
const searchKeyword = ref('') // 搜索关键词
const searchLoading = ref(false) // 搜索状态

// 转写记录数据（初始为空，可以通过按钮添加示例数据）
const transcriptionRecords = ref<TranscriptionRecord[]>([])

// 分页和加载状态
const pagination = ref({
  page: 1,
  size: 15,
  total: 0,
  hasMore: true,
})
const loading = ref(false)
const loadingMore = ref(false)

// 定时器相关状态
let statusCheckTimer: number | null = null
const isCheckingStatus = ref(false)

// 获取转写任务列表
const fetchTranscriptionTasks = async (isLoadMore = false) => {
  try {
    if (isLoadMore) {
      loadingMore.value = true
    } else {
      loading.value = true
    }

    const params = {
      page: pagination.value.page,
      size: pagination.value.size,
      filename: searchKeyword.value || undefined,
    }

    const response = await TranscriptionService.getUserTranscriptionTasks(params)

    const { records, total, pages, current } = response

    // 将后端数据转换为前端需要的格式
    const formattedTasks = records.map((task: TranscriptionTaskData) => ({
      id: task.id,
      fileName: task.fileName,
      transcriptionTime: task.createdAt.substring(0, 10),
      status: task.status,
      aiStatus: task.aiStatus,
      content: task.resultText,
      aiSummary: task.aiSummary,
      objectName: task.objectName,
      userId: task.userId,
      createdAt: task.createdAt.substring(0, 10), // 只保留年月日格式 YYYY-MM-DD
      resultText: task.resultText,
    }))

    if (isLoadMore) {
      // 加载更多：追加数据
      transcriptionRecords.value.push(...formattedTasks)
    } else {
      // 首次加载或搜索：替换数据
      transcriptionRecords.value = formattedTasks
    }

    pagination.value.total = total
    pagination.value.hasMore = current < pages

    // 如果是加载更多且有数据，页码+1
    if (isLoadMore && formattedTasks.length > 0) {
      pagination.value.page++
    }

    // 如果没有数据，显示空状态
    if (!isLoadMore && formattedTasks.length === 0) {
      currentView.value = 'empty'
    } else if (currentView.value === 'empty') {
      currentView.value = 'list'
    }

    // 检查是否需要启动或停止定时器
    checkAndManageStatusTimer()
  } catch (error) {
    console.error('获取转写任务列表失败:', error)
    message.error('获取转写任务列表失败')
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

// 计算属性：是否还有更多数据
const hasMore = computed(() => pagination.value.hasMore)

// 检查是否有需要轮询状态的记录
const hasRecordsNeedingStatusCheck = computed(() => {
  return transcriptionRecords.value.some(
    (record) => record.status === 0 || record.status === 1 || record.aiStatus === 1,
  )
})

// 获取需要检查状态的记录ID数组
const getRecordsNeedingStatusCheck = () => {
  return transcriptionRecords.value
    .filter((record) => record.status === 0 || record.status === 1 || record.aiStatus === 1)
    .map((record) => record.id)
}

// 检查并管理状态定时器
const checkAndManageStatusTimer = () => {
  if (hasRecordsNeedingStatusCheck.value) {
    startStatusCheckTimer()
  } else {
    stopStatusCheckTimer()
  }
}

// 启动状态检查定时器
const startStatusCheckTimer = () => {
  if (statusCheckTimer) {
    return // 定时器已经在运行
  }

  console.log('启动状态检查定时器')
  statusCheckTimer = setInterval(async () => {
    await checkTaskStatus()
  }, 10000) // 每10秒检查一次
}

// 停止状态检查定时器
const stopStatusCheckTimer = () => {
  if (statusCheckTimer) {
    console.log('停止状态检查定时器')
    clearInterval(statusCheckTimer)
    statusCheckTimer = null
  }
}

// 检查任务状态
const checkTaskStatus = async () => {
  if (isCheckingStatus.value || !authStore.isAuthenticated) {
    return
  }

  const recordIds = getRecordsNeedingStatusCheck()
  if (recordIds.length === 0) {
    stopStatusCheckTimer()
    return
  }

  try {
    isCheckingStatus.value = true
    console.log('检查任务状态，记录ID:', recordIds)

    const updatedTasks = await TranscriptionService.getTranscriptionTaskDetail(recordIds)

    updatedTasks.forEach((updatedTask: TranscriptionTaskData) => {
      const index = transcriptionRecords.value.findIndex((record) => record.id === updatedTask.id)

      if (index !== -1) {
        // 更新记录状态
        transcriptionRecords.value[index] = {
          ...transcriptionRecords.value[index],
          status: updatedTask.status,
          aiStatus: updatedTask.aiStatus,
          content: updatedTask.resultText || transcriptionRecords.value[index].content,
          aiSummary: updatedTask.aiSummary || transcriptionRecords.value[index].aiSummary,
        }
      }
    })

    // 检查是否还需要继续轮询
    if (!hasRecordsNeedingStatusCheck.value) {
      stopStatusCheckTimer()
    }
  } catch (error) {
    console.error('检查任务状态失败:', error)
  } finally {
    isCheckingStatus.value = false
  }
}

// 加载更多
const loadMore = (direction: ScrollbarDirection) => {
  if (direction === 'bottom' && hasMore.value && !loadingMore.value) {
    pagination.value.page++
    fetchTranscriptionTasks(true)
  }
}

// 关键字搜索
const search = () => {
  // 重置分页
  pagination.value.page = 1

  // 设置搜索加载状态
  searchLoading.value = true

  // 获取搜索结果
  fetchTranscriptionTasks(false).finally(() => {
    searchLoading.value = false
  })
}

// 方法：选择转写记录
const selectRecord = (record: TranscriptionRecord) => {
  if (record.status !== 2) {
    message.error('转写任务未成功，不能查看详情')
    return
  }
  selectedRecord.value = record
  currentView.value = 'detail'
}

// 方法：返回列表
const backToList = () => {
  currentView.value = 'list'
  selectedRecord.value = null
}

// 方法：删除转写记录
const deleteRecord = async (recordId: string) => {
  try {
    const response = await TranscriptionService.deleteTranscriptionTask(recordId)

    // 判断删除是否成功（状态码为200）
    if (response && response.code === 200) {
      // 从本地数组中移除该记录
      const index = transcriptionRecords.value.findIndex((record) => record.id === recordId)
      if (index > -1) {
        transcriptionRecords.value.splice(index, 1)
        pagination.value.total = Math.max(0, pagination.value.total - 1)
      }

      // 如果删除后没有数据，设置为空状态
      if (transcriptionRecords.value.length === 0) {
        currentView.value = 'empty'
      }

      // 如果当前查看的是被删除的记录，返回列表
      if (selectedRecord.value && selectedRecord.value.id === recordId) {
        backToList()
      }

      message.success('删除成功')

      // 检查是否需要启动或停止定时器
      checkAndManageStatusTimer()
    } else {
      message.error('删除失败')
    }
  } catch (error) {
    console.error('删除转写记录失败:', error)
  }
}

// 方法：AI纪要整理
const generateAISummary = async () => {
  if (selectedRecord.value) {
    // 模拟AI生成摘要
    selectedRecord.value.aiStatus = 1
    const taskId = selectedRecord.value.id
    try {
      const updatedTask = await TranscriptionService.aiSummary(taskId)
      if (selectedRecord.value) {
        selectedRecord.value.aiStatus = 2
        selectedRecord.value.aiSummary = updatedTask.aiSummary
      }
      const index = transcriptionRecords.value.findIndex((record) => record.id === updatedTask.id)

      if (index !== -1) {
        // 更新记录状态
        transcriptionRecords.value[index] = {
          ...transcriptionRecords.value[index],
          aiStatus: updatedTask.aiStatus,
          aiSummary: updatedTask.aiSummary,
        }
      }
      message.success('AI纪要整理完成')
    } catch (error) {
      console.error('生成AI纪要失败:', error)
      message.error('生成AI纪要失败')
    }
  }
}

// 方法：另存为
const saveAs = () => {
  if (!selectedRecord.value || !selectedRecord.value.content) {
    message.error('没有可保存的内容')
    return
  }

  try {
    // 创建文本内容
    const content = selectedRecord.value.content
    const fileName = selectedRecord.value.fileName

    // 创建 Blob 对象
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })

    // 创建下载链接
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url

    // 设置文件名，去掉原文件扩展名并添加 .txt
    const baseFileName = fileName.replace(/\.[^/.]+$/, '')
    link.download = `${baseFileName}_转写结果.txt`

    // 触发下载
    document.body.appendChild(link)
    link.click()

    // 清理
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    message.success('文件已生成')
  } catch (error) {
    console.error('保存文件失败:', error)
    message.error('保存文件失败')
  }
}

// 用户认证相关方法
const handleLogout = () => {
  // 停止状态检查定时器
  stopStatusCheckTimer()

  // 恢复变量到原始状态
  currentView.value = 'empty'
  selectedRecord.value = null
  searchKeyword.value = ''
  searchLoading.value = false
  transcriptionRecords.value = []
  pagination.value = {
    page: 1,
    size: 15,
    total: 0,
    hasMore: true,
  }
  loading.value = false
  loadingMore.value = false
}

const handleLoginSuccess = async () => {
  // 重置分页参数
  pagination.value.page = 1

  // 获取转写任务列表
  await fetchTranscriptionTasks(false)
}

// 检查登录状态的方法
const checkAuthAndExecute = (callback: () => void) => {
  if (!authStore.isAuthenticated) {
    showLoginModal.value = true
    return
  }
  callback()
}

// 处理文件上传前的验证
const handleBeforeUpload = (file: File) => {
  // 使用 useUpload 的 selectFile 方法进行验证和选择
  selectFile(file)
  return false // 阻止默认上传行为
}

// 开始转换（上传并创建转写任务）
const startConversion = async () => {
  if (!currentFile.value) {
    message.error('请先选择文件')
    return
  }

  try {
    // 使用新的上传流程
    const taskData = await startUpload()

    if (taskData) {
      message.success('转写任务创建成功')

      // 重置分页参数
      pagination.value.page = 1

      // 刷新转写任务列表
      await fetchTranscriptionTasks(false)

      // 切换到列表视图
      currentView.value = 'list'

      // 检查是否需要启动定时器（新任务通常是status=0状态）
      checkAndManageStatusTimer()
    }
    // 重置上传状态
    resetUpload()
  } catch (error) {
    console.error('转换失败:', error)
  }
}

// 页面加载时获取用户信息
onMounted(async () => {
  if (authStore.token) {
    try {
      await authStore.getUserInfo()
      // 获取转写任务列表
      await fetchTranscriptionTasks(false)
    } catch (error) {
      console.error('获取用户信息失败:', error)
    }
  } else {
    // 未登录时显示空状态
    currentView.value = 'empty'
  }
})

// 组件卸载时清理定时器
onUnmounted(() => {
  stopStatusCheckTimer()
})
</script>

<style scoped>
.main-content {
  flex: 1;
  width: 100%;
  padding: 0;
}

.content-wrapper {
  display: flex;
  min-height: calc(100vh - 64px - 50px);
  gap: 24px;
  padding: 24px;
  justify-content: center;
  max-width: 2400px;
  margin: 0 auto;
}

/* 左侧音频转换区域 - 响应式布局 1:2 比例，最大800px */
.upload-card {
  flex: 1;
  min-width: 300px;
  max-width: 800px;
}

.upload-section-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  border-radius: 20px;
}

.upload-section-card .ant-card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 32px 24px;
  text-align: center;
}

/* 1. 麦克风图标区域 */
.microphone-section {
  margin-top: 30px;
  margin-bottom: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.microphone-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 120px;
  background: rgba(0, 0, 0, 0.03);
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.03);
}

.microphone-icon-img {
  width: 40%;
  height: 40%;
}

/* 2. 文字描述区域 */
.description-section {
  margin-bottom: 52px;
  text-align: center;
}

.main-title {
  font-size: 28px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
  text-align: center;
}

.description-text {
  font-size: 20px;
  color: rgba(0, 0, 0, 0.6);
  margin: 0 0 30px 0;
  line-height: 1;
  text-align: center;
}

.support-formats {
  font-size: 14px;
  color: #9ca3af;
  margin: 0;
  text-align: center;
}

/* 3. 文件上传区域 */
.upload-section {
  min-height: 260px;
  margin-bottom: 32px;
  display: flex;
  justify-content: center;
}

/* 上传进度区域 */
.upload-progress-section {
  margin: 24px 0;
  padding: 0 24px;
}

.progress-text {
  font-size: 12px;
  color: #666;
  text-align: center;
  margin-top: 8px;
  margin-bottom: 0;
}

.upload-dragger {
  border: 2px dashed #d1d5db !important;
  border-radius: 12px !important;
  background: #f9fafb !important;
  transition: all 0.3s ease;
}

.upload-dragger:hover {
  border-color: #667eea !important;
  background: #f0f4ff !important;
}

/* 自定义上传列表项高度 */
.upload-dragger :deep(.ant-upload-list-item) {
  height: 40px !important;
  line-height: 40px !important;
  padding: 0 12px !important;
}

.upload-dragger :deep(.ant-upload-list-item-info) {
  height: 40px !important;
  line-height: 40px !important;
}

.upload-dragger :deep(.ant-upload-list-item-name) {
  line-height: 40px !important;
}

.upload-dragger :deep(.ant-upload-list-item-actions) {
  height: 40px !important;
  line-height: 40px !important;
}

.upload-content {
  padding: 24px;
}

.upload-icon {
  color: #9ca3af;
  margin-bottom: 12px;
}

.upload-text {
  font-size: 16px;
  color: #374151;
  margin: 0;
  font-weight: 500;
}

/* 4. 开始转换按钮区域 */
.action-section {
  margin-top: auto;
  display: flex;
  justify-content: center;
  align-items: center;
}

.convert-btn-large {
  width: 260px;
  height: 80px;
  background: #1b7846;
  border-radius: 20px;
  font-size: 16px;
  font-weight: 600;
  border: none;
  transition: all 0.3s ease;
}

.convert-btn-large:hover:not(:disabled) {
  background: #249659;
}

.convert-btn-large:disabled {
  background: #e5e7eb;
  color: #9ca3af;
}

/* 右侧转写记录区域 - 响应式布局 1:2 比例，最大1600px */
.transcription-card {
  flex: 2;
  max-width: 1600px;
}

.transcription-section-card {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  border-radius: 20px;
}

.transcription-section-card :deep(.ant-card-body) {
  flex: 1 !important;
  padding: 24px;
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
}

.empty-text {
  font-size: 16px;
  color: #666;
  margin: 0;
  text-align: center;
}

/* 列表状态样式 */
.list-state {
  display: flex;
  flex-direction: column;
  height: 100%;
  align-items: center;
}

.list-header {
  width: 70%;
  margin-bottom: 24px;
}

.list-title {
  font-size: 18px;
  font-weight: 600;
  color: #000;
  margin: 0 0 16px 0;
  text-align: center;
}

.search-container {
  width: 100%;
}

.search-input {
  height: 60px;
  border-radius: 20px;
  border: 1px solid #d9d9d9;
}

.search-input .ant-input {
  height: 58px;
  border: none;
  border-radius: 20px;
  padding: 0 20px;
}

.search-btn {
  color: #1b7846;
  font-weight: bold;
  height: 40px;
  margin-right: 10px;
}

.records-list {
  width: 70%;
  flex: 1;
  overflow-y: auto;
}

.record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  padding: 0 20px;
  border-radius: 20px;
  border: 1px solid #e8e8e8;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.record-item:hover {
  border-color: #52c41a;
  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.15);
}

.record-filename {
  flex: 5;
  max-width: 70%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 14px;
  color: #000;
  font-weight: 500;
}

.record-status {
  flex: 1;
  display: flex;
  align-items: center;
}

.record-actions {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 12px;
}

.record-time {
  font-size: 14px;
  color: #666;
}

.record-list-queue {
  font-size: 14px;
  color: #e3d625;
}

.record-list-transcription {
  font-size: 14px;
  color: #40b21a;
}

.record-list-transcription-success {
  font-size: 14px;
  color: #1b7846;
  font-weight: bold;
}

.record-list-transcription-fail {
  font-size: 14px;
  color: #ff4d4f;
}

.delete-btn {
  color: #ff4d4f;
  font-size: 14px;
  padding: 4px 8px;
  height: auto;
}

/* 详情状态样式 */
.detail-state {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.detail-header {
  margin-bottom: 20px;
}

.back-btn {
  color: #1890ff;
  font-size: 14px;
  padding: 4px 8px;
  height: auto;
}

.detail-content {
  flex: 1;
  overflow: hidden;
}

.single-column {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0;
}

.double-column-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0;
}

.double-column {
  display: flex;
  gap: 24px;
  flex: 1;
  min-height: 0;
}

.left-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.right-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.detail-title {
  font-size: 16px;
  font-weight: 600;
  color: #000;
  margin: 0 0 16px 0;
}

.content-card {
  flex: 1;
  max-height: 750px;
  background: #f5f5f5;
  border-radius: 20px;
  padding: 20px;
  margin-bottom: 16px;
  overflow-y: auto;
  min-height: 0;
}

.summary-card {
  flex: 1;
  max-height: 750px;
  background: #f6ffed;
  border-radius: 20px;
  padding: 20px;
  margin-bottom: 16px;
  overflow-y: auto;
  min-height: 0;
}

.transcription-content,
.summary-content {
  font-size: 14px;
  color: #000;
  line-height: 1.6;
  margin: 0;
  white-space: pre-wrap;
}

.detail-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  width: 100%;
}

.action-btn {
  border-radius: 20px;
  width: 150px;
  height: 50px;
  padding: 0 20px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

/* AI纪要整理按钮样式 */
.action-btn.ai-summary-btn {
  background: #e5e5e5;
  border-color: #e5e5e5;
  color: #333;
}

.action-btn.ai-summary-btn:hover {
  background: #d0d0d0;
  border-color: #d0d0d0;
  color: #333;
}

/* 另存为按钮样式 */
.action-btn.save-as-btn {
  background: #1b7847;
  border-color: #1b7847;
  color: white;
}

.action-btn.save-as-btn:hover {
  background: #249659;
  border-color: #249659;
  color: white;
}

/* 右侧内容区域 */
.content-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  height: 100%;
}

.upload-section {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.upload-dragger {
  background: #fafafa;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  transition: all 0.3s;
  min-height: 150px;
  height: 100%;
}

.upload-dragger:hover {
  border-color: #52c41a;
}

.upload-content {
  padding: 20px;
  text-align: center;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.upload-icon {
  color: #52c41a;
  margin-bottom: 12px;
  display: flex;
  justify-content: center;
}

.upload-text {
  font-size: 14px;
  color: #1f2937;
  margin: 0 0 8px 0;
  font-weight: 500;
}

@media (max-width: 768px) {
  .content-wrapper {
    flex-direction: column;
    height: auto;
    padding: 16px;
    gap: 16px;
  }

  .upload-card,
  .transcription-card {
    flex: none;
    max-width: none;
  }

  .upload-section-card,
  .transcription-section-card {
    height: auto;
  }

  .upload-content {
    padding: 16px;
  }
}
</style>
