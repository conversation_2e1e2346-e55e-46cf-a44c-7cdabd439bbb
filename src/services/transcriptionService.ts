/**
 * 转写任务服务
 */

import request from '@/utils/request'
import type { BaseResponse, TranscriptionTaskListRequest, TranscriptionTaskListData, TranscriptionTaskData } from '@/types/api'

/**
 * 转写任务服务类
 */
export class TranscriptionService {
  /**
   * 获取当前用户的转写任务列表
   * @param params 查询参数
   * @returns 转写任务列表响应
   */
  static async getUserTranscriptionTasks(
    params: TranscriptionTaskListRequest,
  ): Promise<TranscriptionTaskListData> {
    const response = await request.get<BaseResponse<TranscriptionTaskListData>>(
      '/transcription/tasks',
      { params },
    )
    return response.data
  }

  /**
   * 删除转写任务
   * @param taskId 任务ID
   * @returns 删除结果
   */
  static async deleteTranscriptionTask(taskId: string): Promise<BaseResponse<void>> {
    const response = await request.delete<BaseResponse<void>>(`/transcription/tasks/${taskId}`)
    return response
  }

  /**
   * 获取转写任务详情
   * @param recordIds 记录ID数组
   * @returns 转写任务详情响应
   */
  static async getTranscriptionTaskDetail(recordIds: string[]): Promise<BaseResponse<TranscriptionTaskData[]>> {
    const response = await request.get<BaseResponse<TranscriptionTaskData[]>>(
      '/transcription/tasks/detail',
      {
        params: { recordIds: recordIds.join(',') },
    })
    return response.data
  }

  /**
   * AI总结
   * @param taskId 记录ID
   * @returns 转写任务详情响应
   */
  static async aiSummary(taskId: string): Promise<BaseResponse<TranscriptionTaskData>> {
    const response = await request.post<BaseResponse<TranscriptionTaskData>>(
      `/transcription/generateSummary/${taskId}`,
    )
    return response.data
  }
}
