import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import request from '@/utils/request'
import { TOKEN_KEY } from '@/utils/constants'

export interface User {
  userId: string
  username: string
  phone: string
  avatar?: string
  activationCode?: string
  status: string
}

export interface LoginResult {
  tokenInfo: {
    token: string
    invalid_time: number
  }
  userInfo: User
}

export interface BaseResponse<T = unknown> {
  code: number
  message: string
  data?: T
}

export const useAuthStore = defineStore('auth', () => {
  const token = ref<string | null>(localStorage.getItem(TOKEN_KEY))
  const user = ref<User | null>(null)
  const loading = ref(false)

  const isAuthenticated = computed(() => !!token.value)

  // 统一登录/注册接口
  const loginOrRegister = async (phone: string, smsCode: string, activationCode?: string) => {
    loading.value = true
    try {
      const response = await request.post<BaseResponse<LoginResult>>('/user/loginOrRegister', {
        phone,
        smsCode,
        activationCode,
      })

      // 响应拦截器已经处理了code !== 200的情况，这里只处理成功的响应
      if (response.data) {
        const { tokenInfo, userInfo } = response.data

        // 验证必要字段
        if (tokenInfo?.token && userInfo) {
          token.value = tokenInfo.token
          user.value = userInfo
          localStorage.setItem(TOKEN_KEY, tokenInfo.token)
        } else {
          throw new Error('登录响应数据不完整')
        }
      } else {
        throw new Error('登录响应数据为空')
      }

      return response
    } finally {
      loading.value = false
    }
  }

  // 登录（使用统一接口）
  const login = async (phone: string, smsCode: string) => {
    return await loginOrRegister(phone, smsCode)
  }

  // 注册（使用统一接口）
  const register = async (phone: string, smsCode: string, activationCode: string) => {
    return await loginOrRegister(phone, smsCode, activationCode)
  }

  // 微信登录
  const wechatLogin = async (code: string) => {
    loading.value = true
    try {
      const response = await request.post('/auth/wechat-login', {
        code,
      })

      token.value = response.data.token
      user.value = response.data.user
      localStorage.setItem(TOKEN_KEY, response.data.token)

      return response
    } finally {
      loading.value = false
    }
  }

  // 发送短信验证码
  const sendSmsCode = async (phone: string) => {
    return await request.post<BaseResponse>('/user/sendSmsCode', {
      phone,
    })
  }

  // 登出
  const logout = () => {
    token.value = null
    user.value = null
    localStorage.removeItem(TOKEN_KEY)
  }

  // 获取用户信息
  const getUserInfo = async () => {
    if (!token.value) return null

    const response = await request.get<BaseResponse<User>>('/user/info')
    user.value = response.data
    return response.data
  }

  return {
    token,
    user,
    loading,
    isAuthenticated,
    loginOrRegister,
    login,
    wechatLogin,
    register,
    sendSmsCode,
    logout,
    getUserInfo,
  }
})
