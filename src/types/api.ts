/**
 * API 相关类型定义
 */

/**
 * 后台通用响应格式
 */
export interface BaseResponse<T> {
  code: number
  message: string
  data?: T
}

/**
 * 获取上传 URL 响应数据
 */
export interface UploadUrlData {
  /** 预签名上传 URL */
  url: string
  /** OSS 对象名称 */
  objectName: string
  /** 过期时间戳 */
  expiration: number
}

/**
 * 转写任务请求
 */
export interface TranscriptionTaskRequest {
  /** 文件名 */
  fileName: string
  /** 存储文件名，预签名请求中返回的 objectName */
  objectName: string
}

/**
 * 转写任务响应数据
 */
export interface TranscriptionTaskData {
  /** 任务 ID */
  id: string
  /** 用户 ID */
  userId: number
  /** 文件名 */
  fileName: string
  /** 存储文件名 */
  objectName: string
  /** 任务状态 */
  status: number
  /** 转写结果文本 */
  resultText: string
  /** 总结状态 */
  aiStatus: number
  /** 总结文本 */
  aiSummary: string
  /** 创建时间 */
  createdAt: string
}

/**
 * 文件上传状态
 */
export enum UploadStatus {
  PENDING = 'pending',
  UPLOADING = 'uploading',
  SUCCESS = 'success',
  FAILED = 'failed',
}

/**
 * 上传进度信息
 */
export interface UploadProgress {
  /** 已上传字节数 */
  loaded: number
  /** 总字节数 */
  total: number
  /** 上传百分比 */
  percentage: number
}

/**
 * 转写任务列表请求参数
 */
export interface TranscriptionTaskListRequest {
  /** 页码 */
  page: number
  /** 每页大小 */
  size: number
  /** 文件名搜索关键词 */
  filename?: string
}

/**
 * 转写任务列表响应数据
 */
export interface TranscriptionTaskListData {
  /** 记录列表 */
  records: TranscriptionTaskData[]
  /** 总记录数 */
  total: number
  /** 总页数 */
  pages: number
  /** 当前页码 */
  current: number
}
